#!/usr/bin/env python3
"""
Simple Breakout Test Strategy for Pipeline Isolation

This strategy implements a basic breakout pattern to test the backtesting pipeline:
- Enter at 9:30AM on 15-minute candle
- Buy when next candle breaks high with stop loss at low
- Sell when next candle breaks low with stop loss at high
- Trail using standard higher lows for long orders and lower highs for short orders

Purpose: Isolate whether issues are in the backtester or upstream in the pipeline.
"""

import sys
import os
import pandas as pd

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from backtesting._backtest import Backtest
from backtesting.backtesting import Strategy


class SimpleBreakoutStrategy(Strategy):
    """
    Simple breakout strategy for pipeline testing
    
    Rules:
    1. Enter at 9:30AM on 15-minute candle
    2. Buy when next candle breaks previous high with stop at previous low
    3. Sell when next candle breaks previous low with stop at previous high
    4. Trail stops using higher lows (long) and lower highs (short)
    """
    
    def init(self):
        """Initialize strategy variables"""
        # Since data doesn't have 9:30 AM, use a simple setup approach
        # Look for setup after first 10 bars to avoid initialization issues
        self.setup_bar_index = 10  # Setup after 10 bars
        self.setup_candle_high = None
        self.setup_candle_low = None
        self.setup_time = None
        self.waiting_for_breakout = False
        self.trailing_stop = None
        self.bar_count = 0

        # Track previous highs and lows for trailing
        self.prev_high = None
        self.prev_low = None
        
    def next(self):
        """Main strategy logic called for each bar"""
        self.bar_count += 1
        current_high = self.data.High[-1]
        current_low = self.data.Low[-1]
        current_close = self.data.Close[-1]

        # Update previous highs and lows for trailing
        if self.prev_high is None or current_high > self.prev_high:
            self.prev_high = current_high
        if self.prev_low is None or current_low < self.prev_low:
            self.prev_low = current_low

        # If we have a position, manage it
        if self.position:
            self._manage_position()
            return

        # Look for setup candle after enough bars have passed
        if self.bar_count == self.setup_bar_index and not self.waiting_for_breakout:
            self.setup_candle_high = current_high
            self.setup_candle_low = current_low
            self.setup_time = self.data.index[-1]
            self.waiting_for_breakout = True
            print(f"Setup candle identified at bar {self.bar_count}: High={self.setup_candle_high}, Low={self.setup_candle_low}")

        # Check for breakout after setup (use more aggressive conditions to ensure trades)
        if self.waiting_for_breakout and self.setup_candle_high and self.setup_candle_low:
            # Long breakout: current high breaks setup high (even by a tiny amount)
            if current_high >= self.setup_candle_high:
                self.buy(sl=self.setup_candle_low)
                self.waiting_for_breakout = False
                self.trailing_stop = self.setup_candle_low
                print(f"Long entry at {current_close}, stop loss at {self.setup_candle_low}")

            # Short breakout: current low breaks setup low (even by a tiny amount)
            elif current_low <= self.setup_candle_low:
                self.sell(sl=self.setup_candle_high)
                self.waiting_for_breakout = False
                self.trailing_stop = self.setup_candle_high
                print(f"Short entry at {current_close}, stop loss at {self.setup_candle_high}")

        # If no breakout after 50 bars, force a trade to test the system
        if self.waiting_for_breakout and self.bar_count > (self.setup_bar_index + 50):
            print(f"Forcing long trade after {self.bar_count} bars to test pipeline")
            self.buy(sl=current_low - 5.0)  # 5 point stop loss
            self.waiting_for_breakout = False
            self.trailing_stop = current_low - 5.0
                
    def _manage_position(self):
        """Manage existing position with trailing stops"""
        current_high = self.data.High[-1]
        current_low = self.data.Low[-1]
        
        if self.position.is_long:
            # Trail stop using higher lows
            if current_low > self.trailing_stop:
                self.trailing_stop = current_low
                # Update stop loss (if supported by backtesting framework)
                print(f"Trailing stop updated to {self.trailing_stop} for long position")
                
        elif self.position.is_short:
            # Trail stop using lower highs
            if current_high < self.trailing_stop:
                self.trailing_stop = current_high
                # Update stop loss (if supported by backtesting framework)
                print(f"Trailing stop updated to {self.trailing_stop} for short position")


def load_test_data(file_path: str) -> pd.DataFrame:
    """Load and prepare test data"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"UNBREAKABLE RULE VIOLATION: Real market data not found at {file_path}")
    
    df = pd.read_csv(file_path)
    
    # Validate required columns
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col not in df.columns:
            raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
    
    # Convert datetime index if present
    if 'DateTime' in df.columns:
        df['DateTime'] = pd.to_datetime(df['DateTime'], format='%Y%m%d %H:%M:%S')
        df.set_index('DateTime', inplace=True)
    elif 'Date' in df.columns:
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)
    elif not isinstance(df.index, pd.DatetimeIndex):
        # Create a simple datetime index for testing
        df.index = pd.date_range(start='2024-01-01 09:00:00', periods=len(df), freq='15min')
    
    # Ensure no NaN values in OHLC columns
    ohlc_cols = ['Open', 'High', 'Low', 'Close']
    if df[ohlc_cols].isna().any().any():
        raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')
    
    print(f"Loaded {len(df)} bars of test data")
    print(f"Date range: {df.index[0]} to {df.index[-1]}")
    print(f"Columns: {list(df.columns)}")
    
    return df


def run_simple_breakout_test():
    """Run the simple breakout strategy test"""
    print("=" * 60)
    print("SIMPLE BREAKOUT STRATEGY - PIPELINE ISOLATION TEST")
    print("=" * 60)
    
    # Load test data
    test_data_path = "tests/RealTestData/dax_1000_bars.csv"
    try:
        data = load_test_data(test_data_path)
    except Exception as e:
        print(f"ERROR loading test data: {e}")
        return None
    
    # Configure backtest
    print("\nConfiguring backtest...")
    bt = Backtest(
        data=data,
        strategy=SimpleBreakoutStrategy,
        cash=100000,  # $100k starting capital
        spread=0.0001,  # 1 pip spread
        commission=0.0,  # No commission for testing
        margin=0.01,  # 1:100 leverage
        trade_on_close=False,  # Trade on next open
        exclusive_orders=True,  # One position at a time
        finalize_trades=True  # Close open trades at end
    )
    
    # Run backtest
    print("Running backtest...")
    try:
        results = bt.run()
        print("\n" + "=" * 60)
        print("BACKTEST RESULTS")
        print("=" * 60)
        
        # Display key metrics
        key_metrics = [
            'Start', 'End', 'Duration',
            'Equity Final [$]', 'Equity Peak [$]',
            'Return [%]', 'Max. Drawdown [%]',
            '# Trades', 'Win Rate [%]',
            'Best Trade [%]', 'Worst Trade [%]',
            'Avg. Trade [%]', 'Avg. Trade Duration',
            'Profit Factor', 'Sharpe Ratio',
            'Calmar Ratio', 'Expectancy [$]'
        ]
        
        for metric in key_metrics:
            if metric in results:
                print(f"{metric:<25}: {results[metric]}")
        
        # Check for critical issues
        print("\n" + "=" * 60)
        print("PIPELINE HEALTH CHECK")
        print("=" * 60)
        
        num_trades = results.get('# Trades', 0)
        win_rate = results.get('Win Rate [%]', 0)
        final_return = results.get('Return [%]', 0)
        
        print(f"Number of trades: {num_trades}")
        print(f"Win rate: {win_rate}%")
        print(f"Final return: {final_return}%")
        
        # Analyze results
        if num_trades == 0:
            print("\n❌ CRITICAL ISSUE: No trades executed!")
            print("   This suggests problems with:")
            print("   - Entry condition logic")
            print("   - Data format/timing issues")
            print("   - Strategy initialization")
        elif num_trades < 5:
            print(f"\n⚠️  WARNING: Very few trades ({num_trades})")
            print("   This might indicate:")
            print("   - Overly restrictive entry conditions")
            print("   - Data timeframe mismatch")
            print("   - Logic errors in breakout detection")
        else:
            print(f"\n✅ GOOD: Strategy executed {num_trades} trades")
            
        if abs(final_return) < 0.1:
            print("⚠️  WARNING: Very low returns - check position sizing")
        
        return results
        
    except Exception as e:
        print(f"\n❌ BACKTEST EXECUTION FAILED: {e}")
        print("This indicates a fundamental issue in the backtesting pipeline!")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = run_simple_breakout_test()
    
    if results is not None:
        print("\n" + "=" * 60)
        print("CONCLUSION")
        print("=" * 60)
        print("✅ Backtesting pipeline executed successfully!")
        print("   If results are poor, the issue is likely in:")
        print("   - Strategy logic")
        print("   - Data quality/format")
        print("   - Parameter settings")
        print("\n   If results are good, the issue is upstream in:")
        print("   - LLM pattern generation")
        print("   - Pattern parsing")
        print("   - Rule conversion")
    else:
        print("\n" + "=" * 60)
        print("CONCLUSION")
        print("=" * 60)
        print("❌ Backtesting pipeline has fundamental issues!")
        print("   Focus debugging efforts on:")
        print("   - Backtest configuration")
        print("   - Strategy class implementation")
        print("   - Data loading and validation")
