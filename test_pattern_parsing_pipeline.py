#!/usr/bin/env python3
"""
Pattern Parsing Pipeline Test

Tests the pattern parsing and rule conversion modules to isolate issues in:
1. HougaardPatternParser (Idea/backtest_parser.py)
2. SchemaBasedPatternParser (src/backtesting_rule_parser.py)
3. JSON pattern validation and conversion
4. Strategy class generation

Purpose: Continue systematic process of elimination to find root cause of unprofitability.
"""

import sys
import os
import json
import pandas as pd
from typing import Dict, Any

# Add src and Idea directories to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'Idea'))

# Import parsing modules
try:
    from backtest_parser import HougaardPatternParser, run_backtest
    from backtesting_rule_parser import SchemaBasedPatternParser, BacktestingRuleParseError
    from backtesting._backtest import Backtest
    from backtesting.backtesting import Strategy
    IMPORTS_SUCCESS = True
except ImportError as e:
    print(f"Import error: {e}")
    IMPORTS_SUCCESS = False


class PatternParsingTester:
    """Test suite for pattern parsing pipeline"""
    
    def __init__(self):
        self.test_results = {}
        self.sample_patterns = self._create_sample_patterns()
        
    def _create_sample_patterns(self) -> Dict[str, str]:
        """Create sample LLM patterns for testing"""
        
        # Simple breakout pattern (should work)
        simple_pattern = {
            "pattern_name": "Simple Breakout Test",
            "description": "Basic breakout pattern for testing",
            "market_situation": "Range-bound market with breakout potential",
            "entry_conditions": [
                {
                    "condition": "close_above_high",
                    "lookback": 1
                }
            ],
            "exit_conditions": [
                {
                    "condition": "fixed_stop_loss",
                    "percentage": 0.02
                },
                {
                    "condition": "fixed_take_profit", 
                    "percentage": 0.04
                }
            ],
            "entry_logic": "AND",
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.01
            },
            "statistical_edge": {
                "win_rate": 0.60,
                "avg_risk_reward": 2.0,
                "sample_size": 100
            }
        }
        
        # Complex multi-condition pattern (might fail)
        complex_pattern = {
            "pattern_name": "Complex Multi-Condition Pattern",
            "description": "Complex pattern with multiple conditions",
            "market_situation": "Low volatility environment with compression",
            "entry_conditions": [
                {
                    "condition": "consecutive_days",
                    "type": "range_contraction", 
                    "periods": 4,
                    "threshold": 0.20
                },
                {
                    "condition": "volatility_expansion",
                    "threshold": 1.5
                },
                {
                    "condition": "hour_of_day",
                    "start_hour": 9,
                    "end_hour": 11
                }
            ],
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 3
                },
                {
                    "condition": "time_exit",
                    "max_holding_minutes": 240
                }
            ],
            "entry_logic": "AND",
            "filters": [
                {
                    "condition": "low_volatility_regime",
                    "lookback": 20,
                    "threshold": 0.005
                }
            ],
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.02
            }
        }
        
        # Invalid pattern (should fail gracefully)
        invalid_pattern = {
            "pattern_name": "Invalid Pattern Test",
            "description": "Pattern with invalid conditions",
            "entry_conditions": [
                {
                    "condition": "nonexistent_condition",
                    "invalid_param": "test"
                }
            ],
            "exit_conditions": [
                {
                    "condition": "invalid_exit",
                    "bad_param": 123
                }
            ]
        }
        
        return {
            "simple": json.dumps(simple_pattern, indent=2),
            "complex": json.dumps(complex_pattern, indent=2), 
            "invalid": json.dumps(invalid_pattern, indent=2)
        }
    
    def test_hougaard_parser(self) -> Dict[str, Any]:
        """Test HougaardPatternParser from Idea/backtest_parser.py"""
        print("\n" + "="*60)
        print("TESTING HOUGAARD PATTERN PARSER")
        print("="*60)
        
        results = {}
        
        if not IMPORTS_SUCCESS:
            results['error'] = "Failed to import required modules"
            return results
            
        parser = HougaardPatternParser()
        
        for pattern_name, pattern_json in self.sample_patterns.items():
            print(f"\nTesting {pattern_name} pattern...")
            
            try:
                # Test JSON parsing
                pattern_data = parser.parse_llm_output(pattern_json)
                print(f"✅ JSON parsing successful for {pattern_name}")
                print(f"   Pattern name: {pattern_data.get('pattern_name', 'Unknown')}")
                print(f"   Entry conditions: {len(pattern_data.get('entry_conditions', []))}")
                print(f"   Exit conditions: {len(pattern_data.get('exit_conditions', []))}")
                
                # Test strategy class creation
                strategy_class = parser.create_strategy_class(pattern_data)
                print(f"✅ Strategy class creation successful for {pattern_name}")
                print(f"   Class name: {strategy_class.__name__}")
                
                results[pattern_name] = {
                    'json_parsing': 'SUCCESS',
                    'strategy_creation': 'SUCCESS',
                    'pattern_data': pattern_data,
                    'strategy_class': strategy_class
                }
                
            except Exception as e:
                print(f"❌ {pattern_name} pattern failed: {e}")
                results[pattern_name] = {
                    'json_parsing': 'FAILED',
                    'strategy_creation': 'FAILED',
                    'error': str(e)
                }
        
        return results
    
    def test_schema_based_parser(self) -> Dict[str, Any]:
        """Test SchemaBasedPatternParser from src/backtesting_rule_parser.py"""
        print("\n" + "="*60)
        print("TESTING SCHEMA-BASED PATTERN PARSER")
        print("="*60)
        
        results = {}
        
        if not IMPORTS_SUCCESS:
            results['error'] = "Failed to import required modules"
            return results
            
        parser = SchemaBasedPatternParser()
        
        for pattern_name, pattern_json in self.sample_patterns.items():
            print(f"\nTesting {pattern_name} pattern...")
            
            try:
                # Test pattern parsing
                patterns = parser.parse_llm_response(pattern_json)
                print(f"✅ Pattern parsing successful for {pattern_name}")
                print(f"   Number of patterns: {len(patterns)}")
                
                if patterns:
                    pattern = patterns[0]
                    print(f"   Pattern name: {pattern.pattern_name}")
                    print(f"   Entry conditions: {len(pattern.entry_conditions)}")
                    print(f"   Exit conditions: {len(pattern.exit_conditions)}")
                
                # Test function generation
                functions = parser.generate_python_functions()
                print(f"✅ Function generation successful for {pattern_name}")
                print(f"   Number of functions: {len(functions)}")
                
                results[pattern_name] = {
                    'pattern_parsing': 'SUCCESS',
                    'function_generation': 'SUCCESS',
                    'patterns': patterns,
                    'functions': functions
                }
                
            except BacktestingRuleParseError as e:
                print(f"❌ {pattern_name} pattern failed (Parse Error): {e}")
                results[pattern_name] = {
                    'pattern_parsing': 'FAILED',
                    'function_generation': 'FAILED',
                    'error': f"Parse Error: {str(e)}"
                }
            except Exception as e:
                print(f"❌ {pattern_name} pattern failed (General Error): {e}")
                results[pattern_name] = {
                    'pattern_parsing': 'FAILED',
                    'function_generation': 'FAILED',
                    'error': f"General Error: {str(e)}"
                }
        
        return results

    def test_end_to_end_parsing(self) -> Dict[str, Any]:
        """Test complete parsing pipeline with backtesting"""
        print("\n" + "="*60)
        print("TESTING END-TO-END PARSING PIPELINE")
        print("="*60)

        results = {}

        if not IMPORTS_SUCCESS:
            results['error'] = "Failed to import required modules"
            return results

        # Load test data
        test_data_path = "tests/RealTestData/dax_500_bars.csv"
        try:
            if not os.path.exists(test_data_path):
                raise FileNotFoundError(f"UNBREAKABLE RULE VIOLATION: Real market data not found at {test_data_path}")

            data = pd.read_csv(test_data_path)

            # Convert datetime index
            if 'DateTime' in data.columns:
                data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
                data.set_index('DateTime', inplace=True)
            elif not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.date_range(start='2024-01-01 09:00:00', periods=len(data), freq='15min')

            print(f"✅ Test data loaded: {len(data)} bars")

        except Exception as e:
            results['data_loading'] = f"FAILED: {e}"
            return results

        # Test with simple pattern only (most likely to work)
        pattern_json = self.sample_patterns['simple']

        try:
            # Test HougaardPatternParser end-to-end
            print("\nTesting HougaardPatternParser end-to-end...")
            hougaard_results = run_backtest(pattern_json, data)

            print(f"✅ HougaardPatternParser backtest completed")
            print(f"   Pattern: {hougaard_results.get('pattern_name', 'Unknown')}")
            print(f"   Trades: {hougaard_results.get('num_trades', 0)}")
            print(f"   Return: {hougaard_results.get('total_return', 0):.2f}%")
            print(f"   Win Rate: {hougaard_results.get('win_rate', 0):.1f}%")

            results['hougaard_backtest'] = {
                'status': 'SUCCESS',
                'results': hougaard_results
            }

        except Exception as e:
            print(f"❌ HougaardPatternParser backtest failed: {e}")
            results['hougaard_backtest'] = {
                'status': 'FAILED',
                'error': str(e)
            }

        return results

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all parsing pipeline tests"""
        print("="*80)
        print("PATTERN PARSING PIPELINE - SYSTEMATIC TESTING")
        print("="*80)
        print("Testing pattern parsing modules to isolate pipeline issues...")

        all_results = {}

        # Test 1: HougaardPatternParser
        all_results['hougaard_parser'] = self.test_hougaard_parser()

        # Test 2: SchemaBasedPatternParser
        all_results['schema_parser'] = self.test_schema_based_parser()

        # Test 3: End-to-end parsing with backtesting
        all_results['end_to_end'] = self.test_end_to_end_parsing()

        # Analyze results
        self._analyze_results(all_results)

        return all_results

    def _analyze_results(self, results: Dict[str, Any]):
        """Analyze test results and provide conclusions"""
        print("\n" + "="*80)
        print("PATTERN PARSING PIPELINE - ANALYSIS & CONCLUSIONS")
        print("="*80)

        # Check HougaardPatternParser results
        hougaard_results = results.get('hougaard_parser', {})
        if 'error' in hougaard_results:
            print("❌ CRITICAL: HougaardPatternParser import failed")
            print("   This indicates fundamental module issues")
        else:
            simple_success = hougaard_results.get('simple', {}).get('json_parsing') == 'SUCCESS'
            complex_success = hougaard_results.get('complex', {}).get('json_parsing') == 'SUCCESS'

            if simple_success and complex_success:
                print("✅ HougaardPatternParser: JSON parsing works for all patterns")
            elif simple_success:
                print("⚠️  HougaardPatternParser: Simple patterns work, complex patterns fail")
                print("   Issue: Complex pattern logic may be too restrictive")
            else:
                print("❌ HougaardPatternParser: JSON parsing fails for all patterns")
                print("   Issue: Fundamental parsing problems")

        # Check SchemaBasedPatternParser results
        schema_results = results.get('schema_parser', {})
        if 'error' in schema_results:
            print("❌ CRITICAL: SchemaBasedPatternParser import failed")
        else:
            schema_simple = schema_results.get('simple', {}).get('pattern_parsing') == 'SUCCESS'
            if schema_simple:
                print("✅ SchemaBasedPatternParser: Basic functionality works")
            else:
                print("❌ SchemaBasedPatternParser: Basic functionality fails")

        # Check end-to-end results
        e2e_results = results.get('end_to_end', {})
        if 'hougaard_backtest' in e2e_results:
            backtest_status = e2e_results['hougaard_backtest']['status']
            if backtest_status == 'SUCCESS':
                backtest_data = e2e_results['hougaard_backtest']['results']
                num_trades = backtest_data.get('num_trades', 0)

                if num_trades > 0:
                    print("✅ END-TO-END: Complete pipeline works and generates trades")
                    print("   The parsing pipeline is functional!")
                else:
                    print("⚠️  END-TO-END: Pipeline works but generates no trades")
                    print("   Issue: Pattern conditions may be too restrictive")
            else:
                print("❌ END-TO-END: Complete pipeline fails")
                print("   Issue: Integration problems between parsing and backtesting")

        # Final conclusions
        print("\n" + "="*60)
        print("SYSTEMATIC DEBUGGING CONCLUSIONS")
        print("="*60)

        if (hougaard_results.get('simple', {}).get('json_parsing') == 'SUCCESS' and
            e2e_results.get('hougaard_backtest', {}).get('status') == 'SUCCESS'):
            print("🎯 PIPELINE STATUS: Pattern parsing modules are FUNCTIONAL")
            print("\n📍 ROOT CAUSE LIKELY IN:")
            print("   1. LLM pattern generation quality (too complex/restrictive)")
            print("   2. Pattern condition implementation (logic errors)")
            print("   3. Configuration parameters (position sizing, thresholds)")
            print("   4. Data quality or timeframe mismatches")
            print("\n🔄 NEXT STEPS:")
            print("   - Test LLM pattern generation quality")
            print("   - Review pattern condition implementations")
            print("   - Check configuration parameters")
        else:
            print("🎯 PIPELINE STATUS: Pattern parsing modules have ISSUES")
            print("\n📍 ROOT CAUSE LIKELY IN:")
            print("   1. Pattern parsing logic errors")
            print("   2. Strategy class generation problems")
            print("   3. Module import/dependency issues")
            print("\n🔄 NEXT STEPS:")
            print("   - Fix pattern parsing module issues")
            print("   - Debug strategy class generation")
            print("   - Resolve import dependencies")


def main():
    """Main test execution"""
    tester = PatternParsingTester()
    results = tester.run_all_tests()

    # Save results for further analysis
    with open('pattern_parsing_test_results.json', 'w') as f:
        # Convert non-serializable objects to strings
        serializable_results = {}
        for key, value in results.items():
            if isinstance(value, dict):
                serializable_results[key] = {}
                for k, v in value.items():
                    if isinstance(v, dict):
                        serializable_results[key][k] = {
                            str(kk): str(vv) for kk, vv in v.items()
                        }
                    else:
                        serializable_results[key][k] = str(v)
            else:
                serializable_results[key] = str(value)

        json.dump(serializable_results, f, indent=2)

    print(f"\n📄 Test results saved to: pattern_parsing_test_results.json")
    return results


if __name__ == "__main__":
    main()
