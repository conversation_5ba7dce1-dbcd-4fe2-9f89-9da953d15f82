"""
Ho<PERSON><PERSON> Pattern Discovery to Backtesting.py Parser
Converts structured LLM output into backtesting.py compatible strategies
"""

import json
import pandas as pd
from typing import Dict, List, Any, Optional
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
import numpy as np

class HougaardPatternParser:
    """
    Parses LLM-generated Hougaard patterns into backtesting.py compatible strategies
    """
    
    def __init__(self):
        self.supported_conditions = {
            # Price Action Conditions
            'range_contraction': self._range_contraction,
            'range_expansion': self._range_expansion,
            'inside_day': self._inside_day,
            'outside_day': self._outside_day,
            'gap_up': self._gap_up,
            'gap_down': self._gap_down,
            'higher_high': self._higher_high,
            'lower_low': self._lower_low,
            'breakout_above': self._breakout_above,
            'breakdown_below': self._breakdown_below,
            'close_above_high': self._close_above_high,
            'close_below_low': self._close_below_low,
            
            # Volatility Conditions
            'low_volatility_regime': self._low_volatility_regime,
            'high_volatility_regime': self._high_volatility_regime,
            'volatility_expansion': self._volatility_expansion,
            'volatility_compression': self._volatility_compression,
            
            # Time-based Conditions
            'consecutive_days': self._consecutive_days,
            'day_of_week': self._day_of_week,
            'hour_of_day': self._hour_of_day,
            
            # Geometric Patterns
            'measured_move': self._measured_move,
            'retracement': self._retracement,
            'trend_continuation': self._trend_continuation,
            'trend_reversal': self._trend_reversal,
        }
        
        self.supported_exits = {
            'fixed_stop_loss': self._fixed_stop_loss,
            'fixed_take_profit': self._fixed_take_profit,
            'trailing_stop': self._trailing_stop,
            'risk_reward_ratio': self._risk_reward_ratio,
            'time_exit': self._time_exit,
            'pattern_failure': self._pattern_failure,
        }

    def parse_llm_output(self, llm_json: str) -> Dict[str, Any]:
        """
        Parse LLM JSON output into structured pattern data
        
        Expected LLM JSON format:
        {
            "pattern_name": "Volatility Compression Breakout",
            "description": "...",
            "market_situation": "...",
            "entry_conditions": [
                {
                    "condition": "consecutive_days",
                    "type": "range_contraction",
                    "periods": 4,
                    "threshold": 0.20
                },
                {
                    "condition": "volatility_expansion",
                    "threshold": 1.5
                }
            ],
            "entry_logic": "AND",
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 3
                }
            ],
            "filters": [
                {
                    "condition": "low_volatility_regime",
                    "lookback": 20,
                    "threshold": 0.005
                }
            ],
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.02
            },
            "statistical_edge": {
                "win_rate": 0.75,
                "avg_risk_reward": 2.5,
                "sample_size": 150
            }
        }
        """
        try:
            pattern_data = json.loads(llm_json)
            return self._validate_pattern_data(pattern_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
    
    def _validate_pattern_data(self, data: Dict) -> Dict[str, Any]:
        """Validate and normalize pattern data"""
        required_fields = ['pattern_name', 'entry_conditions', 'exit_conditions']
        
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")
        
        # Set defaults
        data.setdefault('entry_logic', 'AND')
        data.setdefault('filters', [])
        data.setdefault('position_sizing', {'method': 'fixed_percent', 'value': 0.02})
        
        return data

    def create_strategy_class(self, pattern_data: Dict) -> type:
        """
        Create a backtesting.py Strategy class from pattern data
        """
        class_name = pattern_data['pattern_name'].replace(' ', '').replace('-', '')
        
        # Create strategy class dynamically
        parser_instance = self  # Capture parser instance in closure

        def init(self):
            self.pattern_data = pattern_data
            self.parser = parser_instance  # Reference to parser instance
            
        def next(self):
            # Check if we already have a position
            if self.position:
                self._check_exit_conditions()
                return
            
            # Check entry conditions
            if self._check_entry_conditions():
                self._execute_entry()
        
        def _check_entry_conditions(self):
            """Check if all entry conditions are met"""
            entry_conditions = self.pattern_data['entry_conditions']
            filters = self.pattern_data.get('filters', [])
            logic = self.pattern_data.get('entry_logic', 'AND')
            
            # Check filters first
            for filter_condition in filters:
                if not self._evaluate_condition(filter_condition):
                    return False
            
            # Check entry conditions
            condition_results = []
            for condition in entry_conditions:
                result = self._evaluate_condition(condition)
                condition_results.append(result)
            
            # Apply logic
            if logic == 'AND':
                return all(condition_results)
            elif logic == 'OR':
                return any(condition_results)
            else:
                return all(condition_results)  # Default to AND
        
        def _evaluate_condition(self, condition: Dict) -> bool:
            """Evaluate a single condition"""
            condition_type = condition['condition']
            
            if condition_type in self.parser.supported_conditions:
                return self.parser.supported_conditions[condition_type](self.data, condition)
            else:
                print(f"Warning: Unsupported condition type: {condition_type}")
                return False
        
        def _execute_entry(self):
            """Execute entry based on pattern"""
            # Determine position size
            size = self._calculate_position_size()
            
            # For now, assume long entry (can be extended for short)
            self.buy(size=size)
        
        def _calculate_position_size(self) -> float:
            """Calculate position size based on pattern specifications"""
            sizing = self.pattern_data.get('position_sizing', {})
            method = sizing.get('method', 'fixed_percent')
            value = sizing.get('value', 0.02)
            
            if method == 'fixed_percent':
                return value
            elif method == 'fixed_amount':
                return value / self.data.Close[-1]  # Convert to percentage
            else:
                return 0.02  # Default 2%
        
        def _check_exit_conditions(self):
            """Check if exit conditions are met"""
            exit_conditions = self.pattern_data['exit_conditions']
            
            for condition in exit_conditions:
                if self._evaluate_exit_condition(condition):
                    self.position.close()
                    return
        
        def _evaluate_exit_condition(self, condition: Dict) -> bool:
            """Evaluate exit condition"""
            condition_type = condition['condition']
            
            if condition_type in self.parser.supported_exits:
                return self.parser.supported_exits[condition_type](self.data, self.position, condition)
            else:
                print(f"Warning: Unsupported exit condition: {condition_type}")
                return False
        
        # Create the strategy class
        strategy_class = type(class_name, (Strategy,), {
            'init': init,
            'next': next,
            '_check_entry_conditions': _check_entry_conditions,
            '_evaluate_condition': _evaluate_condition,
            '_execute_entry': _execute_entry,
            '_calculate_position_size': _calculate_position_size,
            '_check_exit_conditions': _check_exit_conditions,
            '_evaluate_exit_condition': _evaluate_exit_condition,
        })
        
        # Bind parser instance to strategy class
        strategy_class.parser = self
        
        return strategy_class

    # Condition Implementation Methods
    def _range_contraction(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for range contraction over specified periods"""
        periods = params.get('periods', 4)
        threshold = params.get('threshold', 0.20)
        
        if len(data) < periods + 1:
            return False
        
        ranges = data.High - data.Low
        
        for i in range(1, periods + 1):
            if i == 1:
                continue
            
            current_range = ranges.iloc[-i]
            previous_range = ranges.iloc[-i-1]
            
            if previous_range == 0:
                continue
                
            contraction = (previous_range - current_range) / previous_range
            
            if contraction < threshold:
                return False
        
        return True
    
    def _range_expansion(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for range expansion"""
        threshold = params.get('threshold', 1.5)
        lookback = params.get('lookback', 5)
        
        if len(data) < lookback + 1:
            return False
        
        current_range = data.High.iloc[-1] - data.Low.iloc[-1]
        avg_range = (data.High.iloc[-lookback-1:-1] - data.Low.iloc[-lookback-1:-1]).mean()
        
        if avg_range == 0:
            return False
        
        return current_range > avg_range * threshold
    
    def _inside_day(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for inside day pattern"""
        periods = params.get('periods', 1)
        
        if len(data) < periods + 1:
            return False
        
        for i in range(1, periods + 1):
            current_high = data.High.iloc[-i]
            current_low = data.Low.iloc[-i]
            previous_high = data.High.iloc[-i-1]
            previous_low = data.Low.iloc[-i-1]
            
            if current_high >= previous_high or current_low <= previous_low:
                return False
        
        return True
    
    def _outside_day(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for outside day pattern"""
        if len(data) < 2:
            return False
        
        current_high = data.High.iloc[-1]
        current_low = data.Low.iloc[-1]
        previous_high = data.High.iloc[-2]
        previous_low = data.Low.iloc[-2]
        
        return current_high > previous_high and current_low < previous_low
    
    def _gap_up(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for gap up"""
        threshold = params.get('threshold', 0.001)  # 0.1% default
        
        if len(data) < 2:
            return False
        
        current_open = data.Open.iloc[-1]
        previous_close = data.Close.iloc[-2]
        
        gap = (current_open - previous_close) / previous_close
        
        return gap > threshold
    
    def _gap_down(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for gap down"""
        threshold = params.get('threshold', 0.001)  # 0.1% default
        
        if len(data) < 2:
            return False
        
        current_open = data.Open.iloc[-1]
        previous_close = data.Close.iloc[-2]
        
        gap = (previous_close - current_open) / previous_close
        
        return gap > threshold
    
    def _low_volatility_regime(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check if in low volatility regime"""
        lookback = params.get('lookback', 20)
        threshold = params.get('threshold', 0.005)  # 0.5% daily volatility
        
        if len(data) < lookback + 1:
            return False
        
        returns = data.Close.pct_change().iloc[-lookback:]
        volatility = returns.std()
        
        return volatility < threshold
    
    def _volatility_expansion(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for volatility expansion"""
        threshold = params.get('threshold', 1.5)
        lookback = params.get('lookback', 5)
        
        if len(data) < lookback + 1:
            return False
        
        current_range = data.High.iloc[-1] - data.Low.iloc[-1]
        avg_range = (data.High.iloc[-lookback-1:-1] - data.Low.iloc[-lookback-1:-1]).mean()
        
        if avg_range == 0:
            return False
        
        return current_range > avg_range * threshold
    
    def _consecutive_days(self, data: pd.DataFrame, params: Dict) -> bool:
        """Check for consecutive days meeting criteria"""
        periods = params.get('periods', 3)
        criteria_type = params.get('type', 'range_contraction')
        
        # This would delegate to the specific criteria type
        # For now, implement as range contraction
        return self._range_contraction(data, params)
    
    # Exit Condition Methods
    def _fixed_stop_loss(self, data: pd.DataFrame, position, params: Dict) -> bool:
        """Fixed stop loss exit"""
        stop_percent = params.get('percentage', 0.02)
        
        if position.is_long:
            stop_price = position.entry_price * (1 - stop_percent)
            return data.Close.iloc[-1] <= stop_price
        else:
            stop_price = position.entry_price * (1 + stop_percent)
            return data.Close.iloc[-1] >= stop_price
    
    def _fixed_take_profit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        """Fixed take profit exit"""
        profit_percent = params.get('percentage', 0.06)
        
        if position.is_long:
            target_price = position.entry_price * (1 + profit_percent)
            return data.Close.iloc[-1] >= target_price
        else:
            target_price = position.entry_price * (1 - profit_percent)
            return data.Close.iloc[-1] <= target_price
    
    def _risk_reward_ratio(self, data: pd.DataFrame, position, params: Dict) -> bool:
        """Risk-reward ratio exit"""
        risk = params.get('risk', 1)
        reward = params.get('reward', 3)
        
        risk_amount = position.entry_price * 0.02 * risk  # 2% risk per unit
        reward_amount = risk_amount * reward
        
        if position.is_long:
            stop_price = position.entry_price - risk_amount
            target_price = position.entry_price + reward_amount
            
            current_price = data.Close.iloc[-1]
            return current_price <= stop_price or current_price >= target_price
        else:
            stop_price = position.entry_price + risk_amount
            target_price = position.entry_price - reward_amount
            
            current_price = data.Close.iloc[-1]
            return current_price >= stop_price or current_price <= target_price
    
    # Placeholder methods for other conditions
    def _higher_high(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _lower_low(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _breakout_above(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _breakdown_below(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements

    def _close_above_high(self, data, params: Dict) -> bool:
        """Check if current close is above previous high"""
        lookback = params.get('lookback', 1)

        if len(data) < lookback + 1:
            return False

        # Handle both DataFrame and backtesting._Array objects
        if hasattr(data, 'Close') and hasattr(data, 'High'):
            # backtesting._Array object
            current_close = data.Close[-1]
            previous_high = data.High[-1-lookback]
        else:
            # Fallback for other data types
            return False

        return current_close > previous_high

    def _close_below_low(self, data, params: Dict) -> bool:
        """Check if current close is below previous low"""
        lookback = params.get('lookback', 1)

        if len(data) < lookback + 1:
            return False

        # Handle both DataFrame and backtesting._Array objects
        if hasattr(data, 'Close') and hasattr(data, 'Low'):
            # backtesting._Array object
            current_close = data.Close[-1]
            previous_low = data.Low[-1-lookback]
        else:
            # Fallback for other data types
            return False

        return current_close < previous_low
    
    def _high_volatility_regime(self, data: pd.DataFrame, params: Dict) -> bool:
        return not self._low_volatility_regime(data, params)
    
    def _volatility_compression(self, data: pd.DataFrame, params: Dict) -> bool:
        return not self._volatility_expansion(data, params)
    
    def _day_of_week(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _hour_of_day(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _measured_move(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _retracement(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _trend_continuation(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _trend_reversal(self, data: pd.DataFrame, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _trailing_stop(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _time_exit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False  # Implement based on requirements
    
    def _pattern_failure(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False  # Implement based on requirements

def run_backtest(llm_json_output: str, data: pd.DataFrame) -> Dict:
    """
    Main function to run backtest from LLM output
    """
    # Parse LLM output
    parser = HougaardPatternParser()
    pattern_data = parser.parse_llm_output(llm_json_output)
    
    # Create strategy class
    strategy_class = parser.create_strategy_class(pattern_data)
    
    # Run backtest
    bt = Backtest(data, strategy_class, cash=10000, commission=.002)
    results = bt.run()
    
    # Format results
    formatted_results = {
        'pattern_name': pattern_data['pattern_name'],
        'total_return': results['Return [%]'],
        'max_drawdown': results['Max. Drawdown [%]'],
        'sharpe_ratio': results['Sharpe Ratio'],
        'num_trades': results['# Trades'],
        'win_rate': results['Win Rate [%]'],
        'avg_trade': results['Avg. Trade [%]'],
        'raw_results': results
    }
    
    return formatted_results

# Example usage
if __name__ == "__main__":
    # Example LLM output
    example_llm_output = """
    {
        "pattern_name": "Volatility Compression Breakout",
        "description": "Exploits volatility expansion after compression",
        "entry_conditions": [
            {
                "condition": "consecutive_days",
                "type": "range_contraction",
                "periods": 4,
                "threshold": 0.20
            },
            {
                "condition": "volatility_expansion",
                "threshold": 1.5
            }
        ],
        "exit_conditions": [
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 3
            }
        ],
        "filters": [
            {
                "condition": "low_volatility_regime",
                "lookback": 20,
                "threshold": 0.005
            }
        ]
    }
    """
    
    # This would be run with actual market data
    print("Parser ready for LLM output processing")
    print("Supported conditions:", len(HougaardPatternParser().supported_conditions))
    print("Supported exits:", len(HougaardPatternParser().supported_exits))
