{"hougaard_parser": {"simple": {"json_parsing": "SUCCESS", "strategy_creation": "SUCCESS", "pattern_data": "{'pattern_name': 'Simple Breakout Test', 'description': 'Basic breakout pattern for testing', 'market_situation': 'Range-bound market with breakout potential', 'entry_conditions': [{'condition': 'close_above_high', 'lookback': 1}], 'exit_conditions': [{'condition': 'fixed_stop_loss', 'percentage': 0.02}, {'condition': 'fixed_take_profit', 'percentage': 0.04}], 'entry_logic': 'AND', 'position_sizing': {'method': 'fixed_percent', 'value': 0.01}, 'statistical_edge': {'win_rate': 0.6, 'avg_risk_reward': 2.0, 'sample_size': 100}, 'filters': []}", "strategy_class": "<class 'abc.SimpleBreakoutTest'>"}, "complex": {"json_parsing": "SUCCESS", "strategy_creation": "SUCCESS", "pattern_data": "{'pattern_name': 'Complex Multi-Condition Pattern', 'description': 'Complex pattern with multiple conditions', 'market_situation': 'Low volatility environment with compression', 'entry_conditions': [{'condition': 'consecutive_days', 'type': 'range_contraction', 'periods': 4, 'threshold': 0.2}, {'condition': 'volatility_expansion', 'threshold': 1.5}, {'condition': 'hour_of_day', 'start_hour': 9, 'end_hour': 11}], 'exit_conditions': [{'condition': 'risk_reward_ratio', 'risk': 1, 'reward': 3}, {'condition': 'time_exit', 'max_holding_minutes': 240}], 'entry_logic': 'AND', 'filters': [{'condition': 'low_volatility_regime', 'lookback': 20, 'threshold': 0.005}], 'position_sizing': {'method': 'fixed_percent', 'value': 0.02}}", "strategy_class": "<class 'abc.ComplexMultiConditionPattern'>"}, "invalid": {"json_parsing": "SUCCESS", "strategy_creation": "SUCCESS", "pattern_data": "{'pattern_name': 'Invalid Pattern Test', 'description': 'Pattern with invalid conditions', 'entry_conditions': [{'condition': 'nonexistent_condition', 'invalid_param': 'test'}], 'exit_conditions': [{'condition': 'invalid_exit', 'bad_param': 123}], 'entry_logic': 'AND', 'filters': [], 'position_sizing': {'method': 'fixed_percent', 'value': 0.02}}", "strategy_class": "<class 'abc.InvalidPatternTest'>"}}, "schema_parser": {"simple": {"pattern_parsing": "SUCCESS", "function_generation": "SUCCESS", "patterns": "[TradingPattern(pattern_name='Simple Breakout Test', entry_conditions=[{'condition': 'close_above_high', 'lookback': 1}], exit_conditions=[{'condition': 'fixed_stop_loss', 'percentage': 0.02}, {'condition': 'fixed_take_profit', 'percentage': 0.04}], entry_logic='AND', filters=[], position_sizing={'method': 'fixed_percent', 'value': 0.01}, description='Basic breakout pattern for testing', market_situation='Range-bound market with breakout potential', behavioral_logic='', statistical_edge={'win_rate': 0.6, 'avg_risk_reward': 2.0, 'sample_size': 100}, optimal_conditions={}, implementation_notes='')]", "functions": "[<function SchemaBasedPatternParser._create_python_function.<locals>.pattern_function at 0x106d414e0>]"}, "complex": {"pattern_parsing": "SUCCESS", "function_generation": "SUCCESS", "patterns": "[TradingPattern(pattern_name='Complex Multi-Condition Pattern', entry_conditions=[{'condition': 'consecutive_days', 'type': 'range_contraction', 'periods': 4, 'threshold': 0.2}, {'condition': 'volatility_expansion', 'threshold': 1.5}, {'condition': 'hour_of_day', 'start_hour': 9, 'end_hour': 11}], exit_conditions=[{'condition': 'risk_reward_ratio', 'risk': 1, 'reward': 3}, {'condition': 'time_exit', 'max_holding_minutes': 240}], entry_logic='AND', filters=[{'condition': 'low_volatility_regime', 'lookback': 20, 'threshold': 0.005}], position_sizing={'method': 'fixed_percent', 'value': 0.02}, description='Complex pattern with multiple conditions', market_situation='Low volatility environment with compression', behavioral_logic='', statistical_edge={}, optimal_conditions={}, implementation_notes='')]", "functions": "[<function SchemaBasedPatternParser._create_python_function.<locals>.pattern_function at 0x106d41580>]"}, "invalid": {"pattern_parsing": "SUCCESS", "function_generation": "SUCCESS", "patterns": "[TradingPattern(pattern_name='Invalid Pattern Test', entry_conditions=[{'condition': 'nonexistent_condition', 'invalid_param': 'test'}], exit_conditions=[{'condition': 'invalid_exit', 'bad_param': 123}], entry_logic='AND', filters=[], position_sizing={'method': 'fixed_percent', 'value': 0.01}, description='Pattern with invalid conditions', market_situation='', behavioral_logic='', statistical_edge={}, optimal_conditions={}, implementation_notes='')]", "functions": "[<function SchemaBasedPatternParser._create_python_function.<locals>.pattern_function at 0x106d41620>]"}}, "end_to_end": {"hougaard_backtest": {"status": "SUCCESS", "results": "{'pattern_name': 'Simple Breakout Test', 'total_return': 0.0, 'max_drawdown': 0.0, 'sharpe_ratio': 0.0, 'num_trades': 0, 'win_rate': 0.0, 'avg_trade': 0.0, 'raw_results': Start                                                   2024-05-27 00:07:00\nEnd                                                     2024-05-27 10:06:00\nDuration                                                    0 days 09:59:00\nExposure Time [%]                                                     100.0\nEquity Final [$]                                                    10000.0\nEquity Peak [$]                                                     10000.0\nReturn [%]                                                              0.0\nBuy & Hold Return [%]                                                   0.0\nReturn (Ann.) [%]                                                       0.0\nVolatility (Ann.) [%]                                                   0.0\nCAGR [%]                                                                0.0\nSharpe Ratio                                                            0.0\nSortino Ratio                                                           0.0\nCalmar Ratio                                                            0.0\nAlpha [%]                                                               0.0\nBeta                                                                    0.0\nMax. Drawdown [%]                                                       0.0\nAvg. Drawdown [%]                                                       0.0\nMax. Drawdown Duration                                      0 days 00:00:00\nAvg. Drawdown Duration                                      0 days 00:00:00\n# Trades                                                                  0\nWin Rate [%]                                                            0.0\nBest Trade [%]                                                          0.0\nWorst Trade [%]                                                         0.0\nAvg. Trade [%]                                                          0.0\nMax. Trade Duration                                         0 days 00:00:00\nAvg. Trade Duration                                         0 days 00:00:00\nProfit Factor                                                           0.0\nExpectancy [%]                                                          0.0\nSQN                                                                     0.0\nKelly Criterion                                                         0.0\n_strategy                                                SimpleBreakoutTest\n_equity_curve                                   Equity\nDateTime         ...\n_trades                               Empty DataFrame\nColumns: []\nIndex: []\ndtype: object}"}}}